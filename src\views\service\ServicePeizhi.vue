```vue
<!--
  服务配置管理页面
  功能描述：管理服务项目配置，包括配置名称、输入类型、可选项等
  创建时间：2025-01-17
-->

<template>
  <div class="service-config">
    <TopNav title="服务配置管理" />
    <div class="content-container">
      <!-- 搜索表单 -->
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        :inline="true"
        class="search-form"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="项目名称" prop="name">
              <el-input
                v-model="searchForm.name"
                placeholder="请输入项目名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="报价类型" prop="type">
              <el-select
                v-model="searchForm.type"
                placeholder="请选择报价类型"
                clearable
                style="width: 150px"
              >
                <el-option label="一口价" :value="0" />
                <el-option label="报价模式" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <LbButton type="primary" icon="Search" @click="handleSearch">搜索</LbButton>
              <LbButton icon="RefreshLeft" @click="handleReset">重置</LbButton>
              <LbButton type="primary" icon="Plus" @click="handleAdd">新增配置</LbButton>
              <LbButton type="success" icon="Download" @click="handleDownloadTemplate">下载模板</LbButton>
              <LbButton type="warning" icon="Upload" @click="handleBatchImport">批量导入</LbButton>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="ID" width="80" fixed="left" />
          <el-table-column prop="serviceId" label="关联项目ID" width="120" />
          <el-table-column prop="serviceTitle" label="项目名称" width="180" show-overflow-tooltip />
          <el-table-column prop="problemDesc" label="配置名称" width="160" show-overflow-tooltip />
          <el-table-column prop="problemContent" label="配置描述" min-width="250" show-overflow-tooltip />
          <el-table-column prop="inputType" label="配置类型" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="inputTypeTag(row.inputType)">{{ inputTypeText(row.inputType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isRequired" label="是否必填" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.isRequired === 1 ? 'danger' : 'info'">
                {{ row.isRequired === 1 ? '必填' : '非必填' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="报价类型" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.type === 0 ? 'success' : 'warning'">
                {{ row.type === 0 ? '一口价' : '报价模式' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="options" label="可选项" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.inputType === 3 || row.inputType === 4">{{ formatOptions(row.options) }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <LbButton type="primary" @click="handleEdit(row)">编辑</LbButton>
              <LbButton type="danger" @click="handleDelete(row)">删除</LbButton>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑配置' : '新增配置'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="关联项目ID" prop="serviceId">
          <el-input v-model.number="form.serviceId" placeholder="请输入关联项目ID" type="number" />
        </el-form-item>
        <el-form-item label="项目名称" prop="serviceTitle">
          <el-input v-model="form.serviceTitle" placeholder="请输入项目名称（非必填）" />
        </el-form-item>
        <el-form-item label="配置名称" prop="problemDesc">
          <el-input v-model="form.problemDesc" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置描述" prop="problemContent">
          <el-input
            v-model="form.problemContent"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>
        <el-form-item label="报价类型" prop="type">
          <div style="margin-bottom: 10px;">
            <span style="color: #666; font-size: 12px;">当前值: {{ form.type }} ({{ typeof form.type }})</span>
          </div>
          <el-radio-group v-model="form.type" @change="handleTypeChange">
            <el-radio :value="0">一口价 (0)</el-radio>
            <el-radio :value="1">报价模式 (1)</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="配置类型" prop="inputType">
          <el-radio-group v-model="form.inputType" @change="handleInputTypeChange">
            <el-radio :value="1">输入框</el-radio>
            <el-radio :value="2">上传图片</el-radio>
            <el-radio :value="3">单选</el-radio>
            <el-radio :value="4">多选</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.inputType === 3 || form.inputType === 4" label="可选项配置" prop="options">
          <div class="options-config">
            <div v-for="(option, index) in optionsList" :key="index" class="option-item">
              <el-input
                v-model="optionsList[index]"
                placeholder="请输入可选项内容"
                style="width: 300px"
              />
              <LbButton
                type="danger"
                icon="Delete"
                @click="removeOption(index)"
                style="margin-left: 10px"
              >删除</LbButton>
            </div>
            <LbButton type="primary" icon="Plus" @click="addOption" style="margin-top: 10px">
              添加可选项
            </LbButton>
          </div>
        </el-form-item>
        <el-form-item label="是否必填" prop="isRequired">
          <el-radio-group v-model="form.isRequired">
            <el-radio :value="1">必填</el-radio>
            <el-radio :value="0">非必填</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <LbButton @click="dialogVisible = false">取消</LbButton>
        <LbButton type="primary" :loading="submitLoading" @click="handleSubmit">确定</LbButton>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入服务配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>1. 请先下载服务配置模板文件</p>
          <p>2. 按照模板格式填写数据</p>
          <p>3. 上传填写完成的Excel文件</p>
          <p>4. 支持的文件格式：.xlsx</p>
        </el-alert>
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          accept=".xlsx,.xls"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          drag
          style="margin-top: 20px"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">只能上传 xlsx/xls 文件</div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <LbButton @click="importDialogVisible = false">取消</LbButton>
        <LbButton
          type="primary"
          :loading="importLoading"
          :disabled="!selectedFile"
          @click="handleConfirmImport"
        >确认导入</LbButton>
      </template>
    </el-dialog>

    <!-- 导入失败数据对话框 -->
    <el-dialog
      v-model="failDataDialogVisible"
      title="导入失败数据详情"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="fail-data-content">
        <el-alert
          :title="`导入结果：成功 ${importResult.successCount || 0} 条，失败 ${importResult.failCount || 0} 条`"
          type="warning"
          :closable="false"
          show-icon
        />
        <div class="fail-data-table" v-if="importResult.failList?.length">
          <h4>失败数据列表：</h4>
          <el-table :data="importResult.failList" style="width: 100%" max-height="400">
            <el-table-column prop="serviceId" label="服务ID" width="100" />
            <el-table-column prop="problemDesc" label="配置名称" width="150" show-overflow-tooltip />
            <el-table-column prop="problemContent" label="配置描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="type" label="报价类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.type === 0 ? 'success' : 'warning'">
                  {{ row.type === 0 ? '一口价' : '报价模式' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isRequired" label="是否必填" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isRequired === 1 ? 'danger' : 'info'">
                  {{ row.isRequired === 1 ? '必填' : '非必填' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="inputType" label="配置类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="inputTypeTag(row.inputType)">{{ inputTypeText(row.inputType) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="options" label="可选项" min-width="150" show-overflow-tooltip />
          </el-table>
        </div>
        <div class="fail-reasons" v-if="importResult.failReasons?.length">
          <h4>失败原因：</h4>
          <el-alert
            v-for="(reason, index) in importResult.failReasons"
            :key="index"
            :title="`第 ${index + 1} 条数据：${reason}`"
            type="error"
            :closable="false"
            show-icon
            style="margin-bottom: 10px"
          />
        </div>
      </div>
      <template #footer>
        <LbButton @click="failDataDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// Vue 实例和 API
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const api = proxy.$api

// 状态
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const importDialogVisible = ref(false)
const importLoading = ref(false)
const selectedFile = ref(null)
const failDataDialogVisible = ref(false)
const importResult = reactive({
  successCount: 0,
  failCount: 0,
  failList: [],
  failReasons: []
})
const tableData = ref([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  name: '',
  type: null,
  pageNum: 1,
  pageSize: 10
})

// 编辑/新增表单
const form = reactive({
  id: '',
  type: 1, // 默认报价模式
  serviceId: '',
  serviceTitle: '',
  problemDesc: '',
  problemContent: '',
  isRequired: 1,
  inputType: 1,
  val: '',
  options: '[]'
})

const optionsList = ref([])

// 表单验证规则
const formRules = {
  serviceId: [{ required: true, message: '请输入关联项目ID', trigger: 'blur' }],
  problemDesc: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  problemContent: [{ required: true, message: '请输入配置描述', trigger: 'blur' }],
  type: [
    { required: true, message: '请选择报价类型', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value !== 0 && value !== 1) {
          callback(new Error('报价类型必须是“一口价”或“报价模式”'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  inputType: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
  isRequired: [{ required: true, message: '请选择是否必填', trigger: 'change' }]
}

// 表单引用
const formRef = ref(null)
const searchFormRef = ref(null)

// 辅助函数
const inputTypeText = (type) => ({
  1: '输入框',
  2: '上传图片',
  3: '单选',
  4: '多选'
}[type] || '未知')

const inputTypeTag = (type) => ({
  1: 'primary',
  2: 'success',
  3: 'warning',
  4: 'danger'
}[type] || 'info')

const formatOptions = (options) => {
  try {
    return JSON.parse(options || '[]').join(', ') || '-'
  } catch {
    return '-'
  }
}

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize,
      name: searchForm.name || undefined,
      type: searchForm.type !== null ? searchForm.type : undefined
    }
    const { code, data } = await api.service.priceSettingList(params)
    console.log('📋 列表数据:', { code, data })
    if (code === 200 || code === '200') {
      tableData.value = data.list || []
      total.value = data.totalCount || 0
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (error) {
    console.error('获取列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchForm.pageNum = 1
  getList()
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  searchForm.pageNum = 1
  getList()
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getList()
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  isEdit.value = true
  try {
    console.log('📝 编辑配置，ID:', row.id)
    const { code, data } = await api.service.priceSettingInfo({ id: row.id })
    console.log('📋 配置详情:', { code, data, rawType: data.type, rawTypeType: typeof data.type })
    
    if (code === 200 || code === '200') {
      // 严格处理 type
      const typeValue = Number(data.type)
      const finalType = typeValue === 0 || typeValue === 1 ? typeValue : 0 // 默认 0 如果无效
      console.log('📝 处理后的type:', { original: data.type, converted: finalType, typeType: typeof finalType })

      Object.assign(form, {
        id: data.id || '',
        type: finalType,
        serviceId: data.serviceId || '',
        serviceTitle: data.serviceTitle || '',
        problemDesc: data.problemDesc || '',
        problemContent: data.problemContent || '',
        isRequired: Number(data.isRequired) || 1,
        inputType: Number(data.inputType) || 1,
        val: data.val || '',
        options: data.options || '[]'
      })

      console.log('📝 表单填充:', { type: form.type, typeType: typeof form.type, fullForm: { ...form } })

      // 处理可选项
      optionsList.value = (form.inputType === 3 || form.inputType === 4) 
        ? JSON.parse(form.options || '[]') 
        : []
      
      dialogVisible.value = true
      // 触发验证
      setTimeout(() => formRef.value?.validateField('type'), 0)
    } else {
      ElMessage.error('获取详情失败')
    }
  } catch (error) {
    console.error('编辑配置失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定删除配置 "${row.problemDesc}"？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    const { code } = await api.service.priceSettingDelete({ id: row.id })
    if (code === 200 || code === '200') {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 报价类型改变
const handleTypeChange = (value) => {
  console.log('🔄 报价类型变化:', {
    newValue: value,
    newValueType: typeof value,
    oldType: form.type,
    oldTypeType: typeof form.type
  })

  // 直接赋值，不做任何转换
  form.type = value

  console.log('🔄 设置后:', {
    type: form.type,
    typeType: typeof form.type,
    是否为0: form.type === 0,
    是否为1: form.type === 1
  })
}

// 配置类型改变
const handleInputTypeChange = (value) => {
  optionsList.value = (value === 3 || value === 4) ? [''] : []
  form.options = value === 3 || value === 4 ? '[]' : '[]'
}

// 可选项操作
const addOption = () => optionsList.value.push('')
const removeOption = (index) => {
  if (optionsList.value.length > 1) {
    optionsList.value.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个可选项')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    type: 1,
    serviceId: '',
    serviceTitle: '',
    problemDesc: '',
    problemContent: '',
    isRequired: 1,
    inputType: 1,
    val: '',
    options: '[]'
  })
  optionsList.value = []
  formRef.value?.clearValidate()
  console.log('🔄 表单重置:', { type: form.type, typeType: typeof form.type })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 处理可选项
    if (form.inputType === 3 || form.inputType === 4) {
      const validOptions = optionsList.value.filter(opt => opt.trim())
      if (!validOptions.length) {
        ElMessage.error('单选/多选类型需至少一个有效可选项')
        return
      }
      form.options = JSON.stringify(validOptions)
    }

    console.log('📤 提交前检查:', {
      'form.type': form.type,
      'form.type类型': typeof form.type,
      '是否为0': form.type === 0,
      '是否为1': form.type === 1,
      '完整表单': { ...form }
    })

    const submitData = {
      id: form.id,
      type: form.type, // 直接使用，不转换
      serviceId: Number(form.serviceId),
      serviceTitle: form.serviceTitle,
      problemDesc: form.problemDesc,
      problemContent: form.problemContent,
      isRequired: Number(form.isRequired),
      inputType: Number(form.inputType),
      val: form.val,
      options: form.options
    }

    console.log('📤 提交数据:', { type: submitData.type, typeType: typeof submitData.type, fullData: submitData })

    const { code } = isEdit.value
      ? await api.service.priceSettingEdit(submitData)
      : await api.service.priceSettingAdd(submitData)

    if (code === 200 || code === '200') {
      ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getList()
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const result = await api.service.priceSettingTemplate()
    const url = window.URL.createObjectURL(result)
    const link = document.createElement('a')
    link.href = url
    link.download = '服务价格配置导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('模板下载开始')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

// 批量导入
const handleBatchImport = () => {
  importDialogVisible.value = true
  selectedFile.value = null
}

const handleFileChange = (file) => {
  selectedFile.value = file
}

const handleFileRemove = () => {
  selectedFile.value = null
}

const handleConfirmImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请选择文件')
    return
  }
  try {
    importLoading.value = true
    const formData = new FormData()
    formData.append('file', selectedFile.value.raw)
    const { code, data, msg } = await api.service.priceSettingImport(formData)
    if (code === 200 || code === '200') {
      ElMessage.success('导入成功')
      importDialogVisible.value = false
      getList()
    } else if (code === '-1') {
      Object.assign(importResult, {
        successCount: data.successCount || 0,
        failCount: data.failCount || 0,
        failList: data.failList || [],
        failReasons: data.failReasons || []
      })
      ElMessage.warning(`成功${data.successCount}条，失败${data.failCount}条`)
      importDialogVisible.value = false
      failDataDialogVisible.value = true
      getList()
    } else {
      ElMessage.error(msg || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.service-config {
  padding: 20px;
  min-height: 100vh;
}

.content-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.table-container {
  margin: 20px 0;
}

.el-table {
  min-width: 1000px;
}

.options-config {
  border: 1px solid #dcdfe6;
  padding: 15px;
  border-radius: 4px;
  background: #fafafa;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.text-muted {
  color: #909399;
}

.import-content {
  padding: 10px 0;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  padding: 20px;
  text-align: center;
}

.upload-area:hover {
  border-color: #409eff;
}

.el-upload-dragger {
  background: #fafafa;
  height: 180px;
}

.fail-data-content {
  max-height: 70vh;
  overflow-y: auto;
}

.fail-data-table h4, .fail-reasons h4 {
  margin: 0 0 10px;
  font-size: 16px;
  font-weight: 600;
}
</style>
```